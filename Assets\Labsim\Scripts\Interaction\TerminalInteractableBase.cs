using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using PurrNet;
using System;
using UnityEngine.InputSystem;

public abstract class TerminalInteractableBase : InteractableBase , ITerminalComponent
{
    //Events
    public event Action<IInteractor> OnTerminalEntered;
    public event Action<IInteractor> OnTerminalExited;


    private PlayerInputHandler PlayerInputHandler;
    private PlayerInteractor PlayerInteractor;

    public ITerminalServiceProvider ServiceProvider { get; set; }

    protected abstract void HandleTerminalSpecificInteraction(IInteractor interactor);
    
    protected override void OnSpawned()
    {
        base.OnSpawned();
    }
    protected sealed override void HandleInteraction(IInteractor interactor)
    {

        PlayerInputHandler = interactor.Transform.GetComponent<PlayerInputHandler>();
        ServiceProvider.AddService(PlayerInputHandler);
        PlayerController playerController = interactor.Transform.GetComponent<PlayerController>();
        ServiceProvider.AddService(playerController);
        PlayerInteractor = interactor as PlayerInteractor;
        GiveOwnership(interactor.playerID);

        if (PlayerInputHandler == null || PlayerInteractor == null)
        {
            Debug.LogError("Interactor is missing PlayerInputHandler or PlayerInteractor component!");
            return;
        }
        SetInteractableServerRpc(false);
        PlayerInputHandler.playerInput.Player.Disable();
        PlayerInputHandler.playerInput.Terminal.Enable();
        PlayerInteractor.enabled = false;
        PlayerInputHandler.playerInput.Terminal.TerminalExit.performed += ExitTerminal;
        OnTerminalEntered?.Invoke(interactor);
        HandleTerminalSpecificInteraction(interactor);
    }

    protected override void OnOwnerDisconnected(PlayerID ownerId)
    {
        CleanupTerminalInteraction();
    }

    private void ExitTerminal(InputAction.CallbackContext context)
    {
        CleanupTerminalInteraction();
    }

    protected virtual void CleanupTerminalInteraction()
    {
        if (PlayerInputHandler != null)
        {
            PlayerInputHandler.playerInput.Terminal.TerminalExit.performed -= ExitTerminal;
            PlayerInputHandler.playerInput.Terminal.Disable();
            PlayerInputHandler.playerInput.Player.Enable();
        }

        if (PlayerInteractor != null)
        {
            PlayerInteractor.enabled = true;
            OnTerminalExited?.Invoke(PlayerInteractor);
        }

        SetInteractableServerRpc(true);
        GiveOwnership(default);
        PlayerInputHandler = null;
        ServiceProvider.RemoveService<PlayerInputHandler>();
        PlayerInteractor = null;
        ServiceProvider.GetService<PlayerController>().PlayerCrosshair(true);
    }

    [ServerRpc]
    void SetInteractableServerRpc(bool state)
    {
        canInteract.value = state;
    }
}
