using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System;
using Newtonsoft.Json;
using PurrNet;

namespace LabTestSystem
{
    /// <summary>
    /// Server-Authoritative Lab Test Database
    /// Sadece server'da <PERSON><PERSON><PERSON>, client'lar RPC ile erişir
    /// </summary>
    public class LabTestDatabase : NetworkBehaviour
    {
        private static LabTestDatabase _instance;
        public static LabTestDatabase Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<LabTestDatabase>();
                }
                return _instance;
            }
        }

        [Header("Test Definitions")]
        [SerializeField] private List<TestDefinitionSO> availableTestDefinitions = new List<TestDefinitionSO>();
        
        // Database - Sadece server'da tutulur
        private Dictionary<string, PatientTests> patientDatabase;
        private int lastTestInstanceId = 1000;
        
        // Events - Server tarafında
        public static event Action<TestInstance> OnTestCreated;
        public static event Action<TestInstance> OnTestCompleted;
        public static event Action<TestInstance> OnTestUpdated;
        public static event Action<int> OnTestDeleted;
        
        // Client Events - Client'lara bildirim için
        public static event Action<TestInstanceData> OnClientTestCreated;
        public static event Action<TestInstanceData> OnClientTestUpdated;
        public static event Action<int> OnClientTestDeleted;

        private void Awake()
        {
            if (_instance != null && _instance != this)
            {
                Destroy(gameObject);
                return;
            }
            
            _instance = this;
        }

        protected override void OnSpawned(bool asServer)
        {
            base.OnSpawned(asServer);
            
            if (asServer)
            {
                // Sadece server database'i initialize eder
                InitializeServerDatabase();
                Debug.Log("[SERVER] Lab Test Database initialized");
            }
            else
            {
                Debug.Log("[CLIENT] Connected to Lab Test Database");
            }
        }

        private void InitializeServerDatabase()
        {
            if (!isServer) return;
            
            patientDatabase = new Dictionary<string, PatientTests>();
            LoadAllTestDefinitions();
            
            // Server başlarken database'i yükle
            LoadDatabase();
        }

        #region Server-Only Database Operations

        private void LoadAllTestDefinitions()
        {
            if (!isServer) return;
            
            availableTestDefinitions.Clear();
            TestDefinitionSO[] definitions = Resources.LoadAll<TestDefinitionSO>("TestDefinitions");
            availableTestDefinitions.AddRange(definitions);
            Debug.Log($"[SERVER] Loaded {availableTestDefinitions.Count} test definitions");
        }

        private TestDefinitionSO GetTestDefinition(string testDefinitionId)
        {
            if (!isServer) return null;
            return availableTestDefinitions.Find(td => td.testID == testDefinitionId);
        }

        #endregion

        #region Server-Side Test Creation

        /// <summary>
        /// Server tarafında direkt test oluştur (RPC kullanmadan)
        /// </summary>
        public void CreateTestDirectly(string patientId, string testDefinitionId)
        {
            if (!isServer)
            {
                Debug.LogError("[SERVER] CreateTestDirectly can only be called on server!");
                return;
            }

            Debug.Log($"[SERVER] Creating test directly: {testDefinitionId} for patient {patientId}");

            // Test tanımının var olduğunu kontrol et
            TestDefinitionSO definition = GetTestDefinition(testDefinitionId);
            if (definition == null)
            {
                Debug.LogError($"[SERVER] Test definition not found: {testDefinitionId}");
                return;
            }

            // Aynı hasta için aynı test tipinden zaten var mı kontrol et
            if (patientDatabase.ContainsKey(patientId))
            {
                bool testAlreadyExists = patientDatabase[patientId].tests.Any(test => test.testDefinitionId == testDefinitionId);
                if (testAlreadyExists)
                {
                    Debug.LogWarning($"[SERVER] Test {testDefinitionId} already exists for patient {patientId}. Skipping creation.");
                    return;
                }
            }

            // Yeni test instance oluştur
            int newTestId = ++lastTestInstanceId;
            TestInstance newTest = new TestInstance(newTestId, testDefinitionId, patientId);

            // Varsayılan parametre değerlerini ayarla
            foreach (var param in definition.parameters)
            {
                newTest.parameters[param.name] = param.defaultValue;
            }

            // Hasta veritabanına ekle
            if (!patientDatabase.ContainsKey(patientId))
            {
                patientDatabase[patientId] = new PatientData { patientId = patientId, tests = new List<TestInstance>() };
            }
            patientDatabase[patientId].tests.Add(newTest);

            // Test ID mapping'e ekle
            testIdMapping[newTestId] = newTest;

            Debug.Log($"[SERVER] Test created successfully: ID={newTestId}, Type={testDefinitionId}, Patient={patientId}");

            // Tüm client'lara test oluşturulduğunu bildir
            NotifyTestCreatedToAll(new TestInstanceData(newTest));
        }

        #endregion

        #region Client RPCs - Test Creation

        /// <summary>
        /// Client'tan test oluşturma isteği
        /// </summary>
        [ServerRpc]
        public void RequestCreateTest(string patientId, string testDefinitionId, PlayerID sender)
        {
            if (!isServer) return;

            Debug.Log($"[SERVER] Test creation request from {sender}: {testDefinitionId} for patient {patientId}");

            // Test tanımının var olduğunu kontrol et
            TestDefinitionSO definition = GetTestDefinition(testDefinitionId);
            if (definition == null)
            {
                // Client'a hata bildir
                NotifyTestCreationFailed(sender, $"Test definition not found: {testDefinitionId}");
                return;
            }

            // Aynı hasta için aynı test tipinden zaten var mı kontrol et
            if (patientDatabase.ContainsKey(patientId))
            {
                bool testAlreadyExists = patientDatabase[patientId].tests.Any(test => test.testDefinitionId == testDefinitionId);
                if (testAlreadyExists)
                {
                    Debug.LogWarning($"[SERVER] Test {testDefinitionId} already exists for patient {patientId}. Skipping creation.");
                    return;
                }
            }

            // Yeni test instance oluştur
            int newTestId = ++lastTestInstanceId;
            TestInstance newTest = new TestInstance(newTestId, testDefinitionId, patientId);
            
            // Varsayılan parametre değerlerini ayarla
            foreach (var param in definition.parameters)
            {
                newTest.SetParameterResult(param.parameterName, param.defaultValue);
            }

            // Hastanın test koleksiyonuna ekle
            if (!patientDatabase.ContainsKey(patientId))
            {
                patientDatabase[patientId] = new PatientTests(patientId);
            }
            
            patientDatabase[patientId].AddTest(newTest);
            
            // Server event'i
            OnTestCreated?.Invoke(newTest);
            
            // Tüm client'lara bildir
            NotifyTestCreated(new TestInstanceData(newTest));
            
            // İşlemi yapan client'a özel bildirim
            NotifyTestCreationSuccess(sender, new TestInstanceData(newTest));
            
            Debug.Log($"[SERVER] Created test {testDefinitionId} for patient {patientId} with ID: {newTestId}");
        }

        /// <summary>
        /// Birden fazla test oluşturma isteği
        /// </summary>
        [ServerRpc]
        public void RequestCreateMultipleTests(string patientId, string[] testDefinitionIds, PlayerID sender)
        {
            if (!isServer) return;
            
            List<TestInstanceData> createdTests = new List<TestInstanceData>();
            
            foreach (string defId in testDefinitionIds)
            {
                TestDefinitionSO definition = GetTestDefinition(defId);
                if (definition != null)
                {
                    int newTestId = ++lastTestInstanceId;
                    TestInstance newTest = new TestInstance(newTestId, defId, patientId);
                    
                    foreach (var param in definition.parameters)
                    {
                        newTest.SetParameterResult(param.parameterName, param.defaultValue);
                    }

                    if (!patientDatabase.ContainsKey(patientId))
                    {
                        patientDatabase[patientId] = new PatientTests(patientId);
                    }
                    
                    patientDatabase[patientId].AddTest(newTest);
                    OnTestCreated?.Invoke(newTest);
                    
                    createdTests.Add(new TestInstanceData(newTest));
                }
            }
            
            // Client'lara bildir
            NotifyMultipleTestsCreated(createdTests.ToArray());
            NotifyMultipleTestCreationSuccess(sender, createdTests.ToArray());
        }

        #endregion

        #region Client RPCs - Test Updates

        /// <summary>
        /// Test sonucu güncelleme isteği
        /// </summary>
        [ServerRpc]
        public void RequestUpdateTestResult(int instanceTestId, string parameterName, string value, PlayerID sender)
        {
            if (!isServer) return;
            
            TestInstance test = GetTestByIdServer(instanceTestId);
            if (test != null)
            {
                test.SetParameterResult(parameterName, value);
                OnTestUpdated?.Invoke(test);
                
                // Tüm client'lara güncellemeyi bildir
                NotifyTestUpdated(new TestInstanceData(test));
                
                Debug.Log($"[SERVER] Test {instanceTestId} parameter '{parameterName}' updated to '{value}'");
            }
            else
            {
                NotifyOperationFailed(sender, $"Test not found: {instanceTestId}");
            }
        }

        /// <summary>
        /// Birden fazla test sonucu güncelleme
        /// </summary>
        [ServerRpc]
        public void RequestUpdateMultipleResults(int instanceTestId, string[] parameterNames, string[] values, PlayerID sender)
        {
            if (!isServer) return;
            
            if (parameterNames.Length != values.Length)
            {
                NotifyOperationFailed(sender, "Parameter and value arrays must have same length");
                return;
            }
            
            TestInstance test = GetTestByIdServer(instanceTestId);
            if (test != null)
            {
                for (int i = 0; i < parameterNames.Length; i++)
                {
                    test.SetParameterResult(parameterNames[i], values[i]);
                }
                
                OnTestUpdated?.Invoke(test);
                NotifyTestUpdated(new TestInstanceData(test));
                
                Debug.Log($"[SERVER] Test {instanceTestId} multiple parameters updated");
            }
            else
            {
                NotifyOperationFailed(sender, $"Test not found: {instanceTestId}");
            }
        }

        /// <summary>
        /// Test tamamlama isteği
        /// </summary>
        [ServerRpc]
        public void RequestCompleteTest(int instanceTestId, PlayerID sender)
        {
            if (!isServer) return;
            
            TestInstance test = GetTestByIdServer(instanceTestId);
            if (test != null)
            {
                test.CompleteTest();
                OnTestCompleted?.Invoke(test);
                
                NotifyTestCompleted(new TestInstanceData(test));
                
                Debug.Log($"[SERVER] Test {instanceTestId} completed");
            }
            else
            {
                NotifyOperationFailed(sender, $"Test not found: {instanceTestId}");
            }
        }

        #endregion

        #region Client RPCs - Test Queries

        /// <summary>
        /// Hastanın testlerini sorgulama
        /// </summary>
        [ServerRpc]
        public void RequestPatientTests(string patientId, PlayerID sender)
        {
            if (!isServer) return;

            Debug.Log($"[SERVER] RequestPatientTests called for patient: {patientId}");

            List<TestInstanceData> tests = new List<TestInstanceData>();

            if (patientDatabase.ContainsKey(patientId))
            {
                foreach (var test in patientDatabase[patientId].tests)
                {
                    tests.Add(new TestInstanceData(test));
                }
                Debug.Log($"[SERVER] Found {tests.Count} tests for patient {patientId}");
            }
            else
            {
                Debug.LogWarning($"[SERVER] No patient found with ID: {patientId}");
            }

            // İsteyen client'a gönder
            Debug.Log($"[SERVER] Sending {tests.Count} tests to client");
            SendPatientTests(patientId, tests.ToArray());
        }

        /// <summary>
        /// Belirli bir testi sorgulama
        /// </summary>
        [ServerRpc]
        public void RequestTestById(int instanceTestId, PlayerID sender)
        {
            if (!isServer) return;
            
            TestInstance test = GetTestByIdServer(instanceTestId);
            if (test != null)
            {
                SendTestData(sender, new TestInstanceData(test));
            }
            else
            {
                NotifyOperationFailed(sender, $"Test not found: {instanceTestId}");
            }
        }

        /// <summary>
        /// Test istatistiklerini sorgulama
        /// </summary>
        [ServerRpc]
        public void RequestTestStatistics(PlayerID sender)
        {
            if (!isServer) return;
            
            int totalTests = patientDatabase.Values.Sum(pt => pt.tests.Count);
            int completedTests = patientDatabase.Values.Sum(pt => pt.GetTestsByStatus(TestStatus.Completed).Count);
            int pendingTests = patientDatabase.Values.Sum(pt => pt.GetTestsByStatus(TestStatus.Requested).Count);
            
            SendTestStatistics(sender, totalTests, completedTests, pendingTests);
        }

        #endregion

        #region Client RPCs - Test Deletion

        /// <summary>
        /// Test silme isteği
        /// </summary>
        [ServerRpc]
        public void RequestDeleteTest(int instanceTestId, PlayerID sender)
        {
            if (!isServer) return;
            
            bool deleted = false;
            foreach (var patientTests in patientDatabase.Values)
            {
                if (patientTests.RemoveTest(instanceTestId))
                {
                    deleted = true;
                    OnTestDeleted?.Invoke(instanceTestId);
                    
                    // Tüm client'lara bildir
                    NotifyTestDeleted(instanceTestId);
                    
                    Debug.Log($"[SERVER] Deleted test with ID: {instanceTestId}");
                    break;
                }
            }
            
            if (!deleted)
            {
                NotifyOperationFailed(sender, $"Test not found: {instanceTestId}");
            }
        }

        #endregion

        #region Client Notifications (ObserversRpc)

        [ObserversRpc]
        private void NotifyTestCreated(TestInstanceData testData)
        {
            OnClientTestCreated?.Invoke(testData);
            Debug.Log($"[CLIENT] Test created notification: {testData.testDefinitionId}");
        }

        [ObserversRpc]
        private void NotifyMultipleTestsCreated(TestInstanceData[] tests)
        {
            foreach (var test in tests)
            {
                OnClientTestCreated?.Invoke(test);
            }
        }

        [ObserversRpc]
        private void NotifyTestUpdated(TestInstanceData testData)
        {
            OnClientTestUpdated?.Invoke(testData);
            Debug.Log($"[CLIENT] Test updated notification: {testData.instanceTestId}");
        }

        [ObserversRpc]
        private void NotifyTestCompleted(TestInstanceData testData)
        {
            OnClientTestUpdated?.Invoke(testData);
            Debug.Log($"[CLIENT] Test completed notification: {testData.instanceTestId}");
        }

        [ObserversRpc]
        private void NotifyTestDeleted(int instanceTestId)
        {
            OnClientTestDeleted?.Invoke(instanceTestId);
            Debug.Log($"[CLIENT] Test deleted notification: {instanceTestId}");
        }

        #endregion

        #region Targeted Client RPCs

        [TargetRpc]
        private void NotifyTestCreationSuccess(PlayerID target, TestInstanceData testData)
        {
            Debug.Log($"[CLIENT] Successfully created test: {testData.instanceTestId}");
        }

        [TargetRpc]
        private void NotifyMultipleTestCreationSuccess(PlayerID target, TestInstanceData[] tests)
        {
            Debug.Log($"[CLIENT] Successfully created {tests.Length} tests");
        }

        [TargetRpc]
        private void NotifyTestCreationFailed(PlayerID target, string reason)
        {
            Debug.LogError($"[CLIENT] Test creation failed: {reason}");
        }

        [TargetRpc]
        private void NotifyOperationFailed(PlayerID target, string reason)
        {
            Debug.LogError($"[CLIENT] Operation failed: {reason}");
        }

        [ObserversRpc]
        private void SendPatientTests(string patientId, TestInstanceData[] tests)
        {
            Debug.Log($"[CLIENT] Received {tests.Length} tests for patient {patientId}");
            // Client tarafında event'i tetikle
            ClientTestManager.HandlePatientTestsReceived(tests);
        }

        [TargetRpc]
        private void SendTestData(PlayerID target, TestInstanceData testData)
        {
            Debug.Log($"[CLIENT] Received test data: {testData.instanceTestId}");
        }

        [TargetRpc]
        private void SendTestStatistics(PlayerID target, int total, int completed, int pending)
        {
            Debug.Log($"[CLIENT] Statistics - Total: {total}, Completed: {completed}, Pending: {pending}");
        }

        #endregion

        #region Server-Only Helper Methods

        private TestInstance GetTestByIdServer(int instanceTestId)
        {
            if (!isServer) return null;
            
            foreach (var patientTests in patientDatabase.Values)
            {
                TestInstance test = patientTests.GetTest(instanceTestId);
                if (test != null)
                    return test;
            }
            return null;
        }

        #endregion

        #region Data Persistence (Server Only)

        private void SaveDatabase(string filePath = null)
        {
            if (!isServer) return;
            
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    filePath = Application.persistentDataPath + "/labTestDatabase_server.json";
                }

                var saveData = new DatabaseSaveData
                {
                    lastTestInstanceId = lastTestInstanceId,
                    patients = patientDatabase
                };

                string json = JsonConvert.SerializeObject(saveData, Formatting.Indented);
                System.IO.File.WriteAllText(filePath, json);
                
                Debug.Log($"[SERVER] Database saved to: {filePath}");
            }
            catch (Exception e)
            {
                Debug.LogError($"[SERVER] Failed to save database: {e.Message}");
            }
        }

        private void LoadDatabase(string filePath = null)
        {
            if (!isServer) return;
            
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    filePath = Application.persistentDataPath + "/labTestDatabase_server.json";
                }

                if (System.IO.File.Exists(filePath))
                {
                    string json = System.IO.File.ReadAllText(filePath);
                    var saveData = JsonConvert.DeserializeObject<DatabaseSaveData>(json);
                    
                    if (saveData != null)
                    {
                        lastTestInstanceId = saveData.lastTestInstanceId;
                        patientDatabase = saveData.patients ?? new Dictionary<string, PatientTests>();
                        
                        Debug.Log($"[SERVER] Database loaded from: {filePath}");
                    }
                }
                else
                {
                    Debug.LogWarning($"[SERVER] Database file not found: {filePath}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[SERVER] Failed to load database: {e.Message}");
            }
        }

        #endregion

        [System.Serializable]
        private class DatabaseSaveData
        {
            public int lastTestInstanceId;
            public Dictionary<string, PatientTests> patients;
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (isServer && pauseStatus)
                SaveDatabase();
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (isServer && !hasFocus)
                SaveDatabase();
        }

        private void OnDestroy()
        {
            if (isServer)
                SaveDatabase();
        }
    }

    /// <summary>
    /// Network üzerinden gönderilebilir test verisi
    /// </summary>
    [System.Serializable]
    public struct TestInstanceData
    {
        public int instanceTestId;
        public string testDefinitionId;
        public string patientId;
        public TestStatus status;
        public long dateRequestedTicks;
        public long? dateCompletedTicks;
        public string requestedBy;
        public string performedBy;
        public string notes;
        
        // Parametre sonuçları için basitleştirilmiş veri
        public string[] parameterNames;
        public string[] parameterValues;

        public TestInstanceData(TestInstance test)
        {
            instanceTestId = test.instanceTestId;
            testDefinitionId = test.testDefinitionId;
            patientId = test.patientId;
            status = test.status;
            dateRequestedTicks = test.dateRequested.Ticks;
            dateCompletedTicks = test.dateCompleted?.Ticks;
            requestedBy = test.requestedBy;
            performedBy = test.performedBy;
            notes = test.notes;
            
            // Parametre verilerini düzleştir
            var results = test.parameterResults;
            parameterNames = results.Keys.ToArray();
            parameterValues = results.Values.Select(r => r.value).ToArray();
        }
    }
}










// using UnityEngine;
// using System.Collections.Generic;
// using System.Linq;
// using System;
// using Newtonsoft.Json;
// using PurrNet;

// namespace LabTestSystem
// {
//     public class LabTestDatabase : NetworkBehaviour
//     {
//         private static LabTestDatabase _instance;
//         public static LabTestDatabase Instance
//         {
//             get
//             {
//                 if (_instance == null)
//                 {
//                     _instance = FindObjectOfType<LabTestDatabase>();
//                     if (_instance == null)
//                     {
//                         GameObject go = new GameObject("LabTestDatabase");
//                         _instance = go.AddComponent<LabTestDatabase>();
//                         DontDestroyOnLoad(go);
//                     }
//                 }
//                 return _instance;
//             }
//         }

//         [Header("Test Definitions")]
//         [SerializeField] private List<TestDefinitionSO> availableTestDefinitions = new List<TestDefinitionSO>();
        
//         [Header("Database")]
//         [SerializeField] private Dictionary<string, PatientTests> patientDatabase = new Dictionary<string, PatientTests>();
//         [SerializeField] private int lastTestInstanceId = 1000;
        
//         // Events
//         public static event Action<TestInstance> OnTestCreated;
//         public static event Action<TestInstance> OnTestCompleted;
//         public static event Action<TestInstance> OnTestUpdated;
//         public static event Action<int> OnTestDeleted;

//         private void Awake()
//         {
//             if (_instance != null && _instance != this)
//             {
//                 Destroy(gameObject);
//                 return;
//             }
            
//             _instance = this;
//             DontDestroyOnLoad(gameObject);
            
//             InitializeDatabase();
//         }

//         private void InitializeDatabase()
//         {
//             if (patientDatabase == null)
//                 patientDatabase = new Dictionary<string, PatientTests>();
                
//             // Editor'de test tanımlarını yükle
//             #if UNITY_EDITOR
//             LoadAllTestDefinitions();
//             #endif
//         }

//         #region Test Definition Management

//         // Tüm test tanımlarını Resources'dan yükle
//         public void LoadAllTestDefinitions()
//         {
//             availableTestDefinitions.Clear();
//             TestDefinitionSO[] definitions = Resources.LoadAll<TestDefinitionSO>("TestDefinitions");
//             availableTestDefinitions.AddRange(definitions);
//             Debug.Log($"Loaded {availableTestDefinitions.Count} test definitions");
//         }

//         // Test tanımı ekle
//         public void RegisterTestDefinition(TestDefinitionSO definition)
//         {
//             if (definition != null && !availableTestDefinitions.Contains(definition))
//             {
//                 availableTestDefinitions.Add(definition);
//             }
//         }

//         // Test tanımını ID ile getir
//         public TestDefinitionSO GetTestDefinition(string testDefinitionId)
//         {
//             return availableTestDefinitions.Find(td => td.testID == testDefinitionId);
//         }

//         // Tüm test tanımlarını getir
//         public List<TestDefinitionSO> GetAllTestDefinitions()
//         {
//             return new List<TestDefinitionSO>(availableTestDefinitions);
//         }

//         // Kategoriye göre test tanımlarını getir
//         public List<TestDefinitionSO> GetTestDefinitionsByCategory(TestCategory category)
//         {
//             return availableTestDefinitions.Where(td => td.category == category).ToList();
//         }

//         #endregion

//         #region Test Instance Management

//         // Yeni test oluştur
//         public TestInstance CreateTest(string patientId, string testDefinitionId)
//         {
//             // Test tanımının var olduğunu kontrol et
//             TestDefinitionSO definition = GetTestDefinition(testDefinitionId);
//             if (definition == null)
//             {
//                 Debug.LogError($"Test definition not found: {testDefinitionId}");
//                 return null;
//             }

//             // Yeni test instance oluştur
//             int newTestId = ++lastTestInstanceId;
//             TestInstance newTest = new TestInstance(newTestId, testDefinitionId, patientId);
            
//             // Varsayılan parametre değerlerini ayarla
//             foreach (var param in definition.parameters)
//             {
//                 newTest.SetParameterResult(param.parameterName, param.defaultValue);
//             }

//             // Hastanın test koleksiyonuna ekle
//             if (!patientDatabase.ContainsKey(patientId))
//             {
//                 patientDatabase[patientId] = new PatientTests(patientId);
//             }
            
//             patientDatabase[patientId].AddTest(newTest);
            
//             OnTestCreated?.Invoke(newTest);
            
//             Debug.Log($"Created test {testDefinitionId} for patient {patientId} with ID: {newTestId}");
//             return newTest;
//         }

//         // Birden fazla test oluştur
//         public List<TestInstance> CreateMultipleTests(string patientId, List<string> testDefinitionIds)
//         {
//             List<TestInstance> createdTests = new List<TestInstance>();
            
//             foreach (string defId in testDefinitionIds)
//             {
//                 TestInstance test = CreateTest(patientId, defId);
//                 if (test != null)
//                 {
//                     createdTests.Add(test);
//                 }
//             }
            
//             return createdTests;
//         }

//         // Test instance'ı ID ile getir
//         public TestInstance GetTestById(int instanceTestId)
//         {
//             foreach (var patientTests in patientDatabase.Values)
//             {
//                 TestInstance test = patientTests.GetTest(instanceTestId);
//                 if (test != null)
//                     return test;
//             }
//             return null;
//         }

//         // Hastanın tüm testlerini getir
//         public List<TestInstance> GetPatientTests(string patientId)
//         {
//             if (patientDatabase.ContainsKey(patientId))
//             {
//                 return new List<TestInstance>(patientDatabase[patientId].tests);
//             }
//             return new List<TestInstance>();
//         }

//         // Hastanın belirli durumdaki testlerini getir
//         public List<TestInstance> GetPatientTestsByStatus(string patientId, TestStatus status)
//         {
//             if (patientDatabase.ContainsKey(patientId))
//             {
//                 return patientDatabase[patientId].GetTestsByStatus(status);
//             }
//             return new List<TestInstance>();
//         }

//         // Test sonucu güncelle
//         public bool UpdateTestResult(int instanceTestId, string parameterName, string value)
//         {
//             TestInstance test = GetTestById(instanceTestId);
//             if (test != null)
//             {
//                 test.SetParameterResult(parameterName, value);
//                 OnTestUpdated?.Invoke(test);
//                 return true;
//             }
//             return false;
//         }

//         // Birden fazla test sonucu güncelle
//         public bool UpdateMultipleTestResults(int instanceTestId, Dictionary<string, string> results)
//         {
//             TestInstance test = GetTestById(instanceTestId);
//             if (test != null)
//             {
//                 foreach (var kvp in results)
//                 {
//                     test.SetParameterResult(kvp.Key, kvp.Value);
//                 }
//                 OnTestUpdated?.Invoke(test);
//                 return true;
//             }
//             return false;
//         }

//         // Testi tamamla
//         public bool CompleteTest(int instanceTestId)
//         {
//             TestInstance test = GetTestById(instanceTestId);
//             if (test != null)
//             {
//                 test.CompleteTest();
//                 OnTestCompleted?.Invoke(test);
//                 return true;
//             }
//             return false;
//         }

//         // Test sil
//         public bool DeleteTest(int instanceTestId)
//         {
//             foreach (var patientTests in patientDatabase.Values)
//             {
//                 if (patientTests.RemoveTest(instanceTestId))
//                 {
//                     OnTestDeleted?.Invoke(instanceTestId);
//                     Debug.Log($"Deleted test with ID: {instanceTestId}");
//                     return true;
//                 }
//             }
//             return false;
//         }

//         // Hastanın tüm testlerini sil
//         public bool DeleteAllPatientTests(string patientId)
//         {
//             if (patientDatabase.ContainsKey(patientId))
//             {
//                 var tests = patientDatabase[patientId].tests.ToList();
//                 foreach (var test in tests)
//                 {
//                     OnTestDeleted?.Invoke(test.instanceTestId);
//                 }
//                 patientDatabase[patientId].tests.Clear();
//                 return true;
//             }
//             return false;
//         }

//         #endregion

//         #region Data Persistence

//         // Veritabanını JSON'a kaydet
//         public void SaveDatabase(string filePath = null)
//         {
//             try
//             {
//                 if (string.IsNullOrEmpty(filePath))
//                 {
//                     filePath = Application.persistentDataPath + "/labTestDatabase.json";
//                 }

//                 var saveData = new DatabaseSaveData
//                 {
//                     lastTestInstanceId = lastTestInstanceId,
//                     patients = patientDatabase
//                 };

//                 string json = JsonConvert.SerializeObject(saveData, Formatting.Indented);
//                 System.IO.File.WriteAllText(filePath, json);
                
//                 Debug.Log($"Database saved to: {filePath}");
//             }
//             catch (Exception e)
//             {
//                 Debug.LogError($"Failed to save database: {e.Message}");
//             }
//         }

//         // JSON'dan veritabanını yükle
//         public void LoadDatabase(string filePath = null)
//         {
//             try
//             {
//                 if (string.IsNullOrEmpty(filePath))
//                 {
//                     filePath = Application.persistentDataPath + "/labTestDatabase.json";
//                 }

//                 if (System.IO.File.Exists(filePath))
//                 {
//                     string json = System.IO.File.ReadAllText(filePath);
//                     var saveData = JsonConvert.DeserializeObject<DatabaseSaveData>(json);
                    
//                     if (saveData != null)
//                     {
//                         lastTestInstanceId = saveData.lastTestInstanceId;
//                         patientDatabase = saveData.patients ?? new Dictionary<string, PatientTests>();
                        
//                         Debug.Log($"Database loaded from: {filePath}");
//                     }
//                 }
//                 else
//                 {
//                     Debug.LogWarning($"Database file not found: {filePath}");
//                 }
//             }
//             catch (Exception e)
//             {
//                 Debug.LogError($"Failed to load database: {e.Message}");
//             }
//         }

//         // JSON'u import et
//         public void ImportFromJson(string jsonContent)
//         {
//             try
//             {
//                 var saveData = JsonConvert.DeserializeObject<DatabaseSaveData>(jsonContent);
//                 if (saveData != null)
//                 {
//                     lastTestInstanceId = saveData.lastTestInstanceId;
//                     patientDatabase = saveData.patients ?? new Dictionary<string, PatientTests>();
//                     Debug.Log("Database imported successfully");
//                 }
//             }
//             catch (Exception e)
//             {
//                 Debug.LogError($"Failed to import JSON: {e.Message}");
//             }
//         }

//         #endregion

//         #region Statistics and Queries

//         // Toplam test sayısı
//         public int GetTotalTestCount()
//         {
//             return patientDatabase.Values.Sum(pt => pt.tests.Count);
//         }

//         // Belirli durumdaki test sayısı
//         public int GetTestCountByStatus(TestStatus status)
//         {
//             return patientDatabase.Values.Sum(pt => pt.GetTestsByStatus(status).Count);
//         }

//         // En çok kullanılan test türleri
//         public Dictionary<string, int> GetTestUsageStatistics()
//         {
//             Dictionary<string, int> stats = new Dictionary<string, int>();
            
//             foreach (var patientTests in patientDatabase.Values)
//             {
//                 foreach (var test in patientTests.tests)
//                 {
//                     if (stats.ContainsKey(test.testDefinitionId))
//                         stats[test.testDefinitionId]++;
//                     else
//                         stats[test.testDefinitionId] = 1;
//                 }
//             }
            
//             return stats;
//         }

//         // Tarih aralığındaki testleri getir
//         public List<TestInstance> GetTestsByDateRange(DateTime startDate, DateTime endDate)
//         {
//             List<TestInstance> tests = new List<TestInstance>();
            
//             foreach (var patientTests in patientDatabase.Values)
//             {
//                 tests.AddRange(patientTests.tests.Where(t => 
//                     t.dateRequested >= startDate && t.dateRequested <= endDate));
//             }
            
//             return tests;
//         }

//         #endregion

//         // Kayıt verisi sınıfı
//         [System.Serializable]
//         private class DatabaseSaveData
//         {
//             public int lastTestInstanceId;
//             public Dictionary<string, PatientTests> patients;
//         }

//         private void OnApplicationPause(bool pauseStatus)
//         {
//             if (pauseStatus)
//                 SaveDatabase();
//         }

//         private void OnApplicationFocus(bool hasFocus)
//         {
//             if (!hasFocus)
//                 SaveDatabase();
//         }

//         private void OnDestroy()
//         {
//             SaveDatabase();
//         }
//     }
// }