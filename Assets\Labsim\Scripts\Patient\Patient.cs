using UnityEngine;
using PurrNet;
using Labsim;
using UnityEngine.Animations.Rigging;
using System.Collections.Generic;
using LabTestSystem;
using System.Collections;


public class Patient : NetworkBehaviour
{
   // private ILabDatabaseService _labDatabaseService;

    //Same Object References
    [SerializeField] private PatientBracelet bracelet;
    [SerializeField] public Rig handRig;


    //Synced variables
    [SerializeField] public SyncVar<int> patientId = new SyncVar<int>(2001);
    

    [Header("Test Settings")]
    public int minRandomTestsToAssign = 2; 
    public int maxRandomTestsToAssign = 3;




    // public void Initialize(ILabDatabaseService labDatabaseService)
    // {
    //     _labDatabaseService = labDatabaseService;
    // }

    private void Awake()
    {
        InitializeComponents();
    }

    protected override void OnSpawned()
    {
        InitializeListeningEvents();
        ClientTestManager.Instance.CreateTest(patientId.value.ToString(), "HEMOGRAM");
        ClientTestManager.Instance.CreateTest(patientId.value.ToString(), "COAGULATION");
    }


    public void InitializeComponents()
    {
        handRig = GetComponentInChildren<Rig>();
        bracelet = GetComponentInChildren<PatientBracelet>();
    }


    //Listening events
    void InitializeListeningEvents()
    {
        bracelet.OnBraceletInteract += HandleBraceletInteract;
    }

    //Listening events functions

    private void HandleBraceletInteract(TerminalInteractor interactor)
    {
        Debug.Log("Patient bracelet scanned in Patient");
    }

   

}